{"format": 1, "restore": {"C:\\docker_cp\\csapi\\csapisample\\csapisample.csproj": {}}, "projects": {"C:\\docker_cp\\csapi\\csapisample\\csapisample.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\docker_cp\\csapi\\csapisample\\csapisample.csproj", "projectName": "csapisample", "projectPath": "C:\\docker_cp\\csapi\\csapisample\\csapisample.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\docker_cp\\csapi\\csapisample\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "scriptcs": {"target": "Package", "version": "[0.17.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}